from fastapi import <PERSON><PERSON><PERSON>, Request, status, Depends
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from app.api import user, auth
from app.models import create_tables
from app.config import settings
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="API for validating users against Microsoft Entra ID (Active Directory)",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers - Authentication first, then User
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(user.router, prefix="/api", tags=["User"])

# Custom OpenAPI schema to filter endpoints
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    # Get the default OpenAPI schema
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Filter paths to only include those with "User" or "Authentication" tags
    filtered_paths = {}
    allowed_tags = {"User", "Authentication"}

    for path, path_item in openapi_schema["paths"].items():
        # Check if any operation in this path has allowed tags
        should_include = False
        for method, operation in path_item.items():
            if method in ["get", "post", "put", "delete", "patch", "options", "head"]:
                operation_tags = operation.get("tags", [])
                if any(tag in allowed_tags for tag in operation_tags):
                    should_include = True
                    break

        if should_include:
            filtered_paths[path] = path_item

    # Update the schema with filtered paths
    openapi_schema["paths"] = filtered_paths

    # Ensure tags are ordered correctly (Authentication first, then User)
    openapi_schema["tags"] = [
        {"name": "Authentication", "description": "Authentication and token management endpoints"},
        {"name": "User", "description": "User validation endpoints"}
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Set the custom OpenAPI schema
app.openapi = custom_openapi

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    logger.info("Creating database tables if they don't exist")
    create_tables()
    logger.info("Application startup complete")

# Custom exception handlers
@app.exception_handler(status.HTTP_401_UNAUTHORIZED)
async def unauthorized_exception_handler(request: Request, exc):
    # Check if it's a token expiration error
    if hasattr(exc, "detail") and exc.detail == "Token Expired":
        # Log the token expiration error
        logger.info(f"Token expired error: {exc.detail}")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"status": "error", "message": "Token Expired"},
        )
    # Check if it's an invalid token error
    elif hasattr(exc, "detail") and exc.detail == "Invalid Token Key":
        # Log the invalid token error
        logger.info(f"Invalid token error: {exc.detail}")
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={"status": "error", "message": "Invalid Token Key"},
        )
    # Default unauthorized response
    logger.info(f"Unauthorized error: {exc.detail if hasattr(exc, 'detail') else 'No detail'}")
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content={"status": "error", "message": "Invalid User"},
    )

@app.exception_handler(status.HTTP_503_SERVICE_UNAVAILABLE)
async def service_unavailable_exception_handler(request: Request, exc):
    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={"status": "error", "message": "Authentication server unavailable"},
    )

# Root endpoint
@app.get("/")
async def root():
    return {"message": f"Welcome to {settings.app_name}"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy"}
