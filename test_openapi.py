#!/usr/bin/env python3
"""
Test script to verify the custom OpenAPI schema filtering
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Mock the database connection to avoid startup errors
import unittest.mock

# Mock the database models and functions
with unittest.mock.patch('app.models.create_tables'):
    with unittest.mock.patch('app.models.get_db'):
        with unittest.mock.patch('app.services.entra_service.EntraService'):
            from app.main import app
            
            # Get the OpenAPI schema
            openapi_schema = app.openapi()
            
            print("=== OpenAPI Schema Test ===")
            print(f"Title: {openapi_schema.get('info', {}).get('title')}")
            print(f"Version: {openapi_schema.get('info', {}).get('version')}")
            print()
            
            # Check tags
            print("=== Tags ===")
            tags = openapi_schema.get('tags', [])
            for i, tag in enumerate(tags):
                print(f"{i+1}. {tag.get('name')} - {tag.get('description')}")
            print()
            
            # Check paths
            print("=== Filtered Paths ===")
            paths = openapi_schema.get('paths', {})
            for path, path_item in paths.items():
                print(f"Path: {path}")
                for method, operation in path_item.items():
                    if method in ["get", "post", "put", "delete", "patch", "options", "head"]:
                        operation_tags = operation.get('tags', [])
                        summary = operation.get('summary', 'No summary')
                        print(f"  {method.upper()}: {summary} (tags: {operation_tags})")
                print()
            
            # Verify filtering worked
            print("=== Verification ===")
            allowed_tags = {"User", "Authentication"}
            all_operation_tags = set()
            
            for path, path_item in paths.items():
                for method, operation in path_item.items():
                    if method in ["get", "post", "put", "delete", "patch", "options", "head"]:
                        operation_tags = operation.get('tags', [])
                        all_operation_tags.update(operation_tags)
            
            print(f"All operation tags found: {all_operation_tags}")
            print(f"Allowed tags: {allowed_tags}")
            
            # Check if only allowed tags are present
            unexpected_tags = all_operation_tags - allowed_tags
            if unexpected_tags:
                print(f"❌ ERROR: Found unexpected tags: {unexpected_tags}")
            else:
                print("✅ SUCCESS: Only allowed tags found")
            
            # Check tag order
            tag_names = [tag.get('name') for tag in tags]
            expected_order = ['Authentication', 'User']
            if tag_names == expected_order:
                print("✅ SUCCESS: Tags are in correct order (Authentication first, then User)")
            else:
                print(f"❌ ERROR: Tags are not in correct order. Expected: {expected_order}, Got: {tag_names}")
